/* eslint-disable no-unused-vars */
/* eslint-disable react/prop-types */
import { useRef, useEffect, useMemo, useState } from "react";
import * as THREE from "three";
import { useFrame } from "@react-three/fiber";
import { SimplexNoise } from "three/addons/math/SimplexNoise.js";
import { Line2 } from "three/examples/jsm/lines/Line2.js";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry.js";
import { LineMaterial } from "three/examples/jsm/lines/LineMaterial.js";
import { SpriteItem } from "./SpriteItem";

const vertShader = `
attribute float scale;
varying float vAlpha;

void main() {
    vec4 mvPosition = modelViewMatrix * vec4( position, 1 );
    gl_PointSize = 1.8;
    gl_Position = projectionMatrix * mvPosition;

    vAlpha = scale;
}
`;

const fragShader = `
uniform vec3 color;
varying float vAlpha;

void main() {
    if ( length( gl_PointCoord - vec2( 0.5, 0.5 ) ) > 0.9 ) discard;
    gl_FragColor = vec4( color, vAlpha * 0.8 ); // apply fade
}
`;

export const Particles = ({ lineConfigs = [], timelines, visibleTimelines, openCards, setOpenCards, setCameraRef, onYearChange, onWaveAnimationComplete, startWaveAnimation }) => {
  const SEPARATION = 0.005;
  const AMOUNTX = Math.max(timelines.length * 47, 47); // Ensure at least one row for initial render
  const AMOUNTY = 90;

  const particlesRef = useRef();
  const spriteRef = useRef();
  const lineRefs = useRef([]);
  const lineMatRefs = useRef([]);
  const lastYearRef = useRef(null);

  const simplex = useMemo(() => new SimplexNoise(), []);
  const waveShift = useRef(0);
  const [waveOffset, setWaveOffset] = useState(-0.5); // Start wave below normal position
  const [isWaveAnimating, setIsWaveAnimating] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false); // Track if animation has run

  // Initialize line references arrays
  useEffect(() => {
    if (lineConfigs.length > 0) {
      lineRefs.current = lineRefs.current.slice(0, lineConfigs.length);
      lineMatRefs.current = lineMatRefs.current.slice(0, lineConfigs.length);
    }
  }, [lineConfigs.length]);

  // Reset animation state when startWaveAnimation changes to false
  useEffect(() => {
    if (!startWaveAnimation) {
      setWaveOffset(-0.5);
      setHasAnimated(false);
    }
  }, [startWaveAnimation]);

  // Trigger wave animation based on startWaveAnimation prop
  useEffect(() => {
    // console.log("Wave animation effect triggered", {
    //   startWaveAnimation,
    //   waveOffset,
    //   hasAnimated,
    //   timelinesLength: timelines.length
    // });

    if (startWaveAnimation && waveOffset < 0 && !hasAnimated) {
      // console.log("Starting wave animation");
      setIsWaveAnimating(true);
      setHasAnimated(true); // Mark animation as run
      const duration = 2000; // 2 seconds for wave animation
      const startTime = performance.now();

      const animate = (time) => {
        const elapsed = time - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const ease = 1 - Math.pow(1 - progress, 3); // Ease-out cubic
        setWaveOffset(-0.5 + ease * 0.5);

        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // console.log("Wave animation complete, calling onWaveAnimationComplete");
          setIsWaveAnimating(false);
          if (onWaveAnimationComplete) {
            onWaveAnimationComplete(timelines.length);
          }
        }
      };

      requestAnimationFrame(animate);
    }
  }, [startWaveAnimation, onWaveAnimationComplete, timelines.length, waveOffset, hasAnimated]);

  // Synchronize openCards with visibleTimelines
  useEffect(() => {
    const visibleIndices = visibleTimelines.map((item) => timelines.findIndex((t) => t._id === item._id));
    setOpenCards((prev) => {
      const filteredCards = prev.filter((card) => visibleIndices.includes(card.index));
      return filteredCards;
    });
  }, [visibleTimelines, timelines, setOpenCards]);

  useEffect(() => {
    const handleResize = () => {
      if (lineMatRefs.current) {
        lineMatRefs.current.forEach((mat) => {
          if (mat) mat.resolution.set(window.innerWidth, window.innerHeight);
        });
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (!particlesRef.current) return;

    const numParticles = AMOUNTX * AMOUNTY;
    const positions = new Float32Array(numParticles * 3);
    const scales = new Float32Array(numParticles);

    let i = 0,
      j = 0;
    for (let ix = 0; ix < AMOUNTX; ix++) {
      for (let iy = 0; iy < AMOUNTY; iy++) {
        positions[i] = ix * SEPARATION;
        positions[i + 1] = 0;
        positions[i + 2] = iy * SEPARATION - (AMOUNTY * SEPARATION) / 2;

        const fade = Math.sin((Math.PI * ix) / AMOUNTX);
        scales[j] = fade;

        i += 3;
        j++;
      }
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute("scale", new THREE.BufferAttribute(scales, 1));

    const material = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: new THREE.Color(0xffffff) },
      },
      vertexShader: vertShader,
      fragmentShader: fragShader,
      transparent: true,
    });

    particlesRef.current.geometry = geometry;
    particlesRef.current.material = material;
  }, [AMOUNTX]);

  useFrame(({ clock, camera }) => {
    if (!particlesRef.current || !particlesRef.current.geometry.attributes.position) return;

    const positions = particlesRef.current.geometry.attributes.position.array;
    const time = clock.getElapsedTime();

    waveShift.current += 0.001;

    let i = 0,
      j = 0;
    for (let ix = 0; ix < AMOUNTX; ix++) {
      for (let iy = 0; iy < AMOUNTY; iy++) {
        const x = ix * SEPARATION;
        const z = iy * SEPARATION;

        const y = (0.10 * simplex.noise3d(x / 0.62 + waveShift.current, z / 0.33, time / 30)) + waveOffset;

        positions[i + 1] = y;
        i += 3;
        j++;
      }
    }

    particlesRef.current.geometry.attributes.position.needsUpdate = true;
    particlesRef.current.geometry.attributes.scale.needsUpdate = true;

    if (spriteRef.current) {
      spriteRef.current.children.forEach((el, idx) => {
        if (el && el.isObject3D) {
          const spriteId = el.userData.id;
          const originalIndex = timelines.findIndex((item) => item._id === spriteId);
          if (originalIndex !== -1) {
            const reversedIndex = timelines.length - 1 - originalIndex;
            const positionIndex = reversedIndex * 47 * AMOUNTY * 3;
            if (positionIndex >= 0 && positionIndex < positions.length) {
              el.position.set(
                positions[positionIndex],
                positions[positionIndex + 1],
                positions[positionIndex + 2]
              );
            }
          }
        }
      });
    }

    // Update multiple lines with different wave patterns
    lineRefs.current.forEach((lineRef, lineIndex) => {
      if (lineRef && lineRef.geometry && lineIndex < lineConfigs.length) {
        const linePoints = [];
        let index = 0;

        // Create very subtle variations while maintaining the original wave pattern
        // The original wave goes through every hotspot, so we need to preserve that
        const subtleOffset = lineIndex * 0.008; // Increased offset for more noticeable gap
        const phaseShift = lineIndex * 0.2; // Increased phase shift for better wave differentiation

        for (let i = 0; i < AMOUNTX; i++) {
          const baseX = positions[index];
          const baseY = positions[index + 1];
          const baseZ = positions[index + 2];
          
          // Add more noticeable variation to Y position while maintaining hotspot alignment
          // Use different wave patterns for each line to create distinct but similar waves
          const wavePattern = lineIndex === 0 ? 0 : 
                             lineIndex === 1 ? Math.sin(i * 0.03 + phaseShift) :
                             Math.sin(i * 0.04 + phaseShift) + 0.5 * Math.sin(i * 0.02 + phaseShift);
          
          const variationY = baseY + (subtleOffset * wavePattern);
          
          linePoints.push(baseX, variationY, baseZ);
          index += AMOUNTY * 3;
        }

        lineRef.geometry.setPositions(linePoints);

        const lineConfig = lineConfigs[lineIndex];
        const lineMatRef = lineMatRefs.current[lineIndex];
        
        if (lineMatRef) {
          const shouldHideLine = !lineConfig.active || lineConfig.color === "transparent" || lineConfig.color === "none" || lineConfig.color === "rgba(0,0,0,0)";
          if (!shouldHideLine) {
            lineMatRef.color.set(lineConfig.color);
            lineMatRef.opacity = 1;
          } else {
            lineMatRef.color.set("#000000");
            lineMatRef.opacity = 0;
          }
        }
      }
    });

    if (timelines.length > 0 && onYearChange && spriteRef.current) {
      const cameraX = camera.position.x;
      let closestIndex = 0;
      let minDistance = Infinity;

      spriteRef.current.children.forEach((child, idx) => {
        if (child && child.position) {
          const distance = Math.abs(child.position.x - cameraX);
          if (distance < minDistance) {
            minDistance = distance;
            closestIndex = timelines.findIndex((item) => item._id === child.userData.id);
          }
        }
      });

      if (closestIndex >= 0 && closestIndex < timelines.length) {
        const year = timelines[closestIndex]?.year;
        if (year && year !== lastYearRef.current) {
          lastYearRef.current = year;
          onYearChange(year);
        }
      }
    }
  });

  useEffect(() => {
    if (setCameraRef && spriteRef.current) {
      const navigateToNode = (index) => {
        if (index >= 0 && index < timelines.length) {
          setCameraRef((navigate) => {
            if (navigate) navigate(index);
            return navigate;
          });
        }
      };

      setCameraRef(navigateToNode);
    }
  }, [setCameraRef, timelines.length]);

  const Sprites = useMemo(
    () =>
      timelines.map((item, i) => {
        const isVisible = visibleTimelines.some((visibleItem) => visibleItem._id === item._id);
        if (!isVisible) return null;

        return (
          <SpriteItem
            key={i}
            item={item}
            index={i}
            openCards={openCards}
            setOpenCards={setOpenCards}
            userData={{ id: item._id }}
          />
        );
      }),
    [timelines, visibleTimelines, openCards, setOpenCards]
  );

  // Create multiple lines based on lineConfigs
  const Lines = useMemo(() => {
    return lineConfigs.map((lineConfig, index) => {
      const geometry = new LineGeometry();
      const initialPositions = new Float32Array(AMOUNTX * 3);
      geometry.setPositions(initialPositions);
      
      const material = new LineMaterial({
        color: lineConfig.color,
        linewidth: 4,
        vertexColors: false,
        dashed: false,
        alphaToCoverage: false,
        transparent: true,
        opacity: lineConfig.active ? 1 : 0,
        resolution: new THREE.Vector2(window.innerWidth, window.innerHeight),
      });
      
      lineMatRefs.current[index] = material;
      const line = new Line2(geometry, material);
      
      return (
        <primitive
          key={`line-${index}`}
          object={line}
          ref={(ref) => {
            if (ref) {
              lineRefs.current[index] = ref;
            }
          }}
        />
      );
    });
  }, [lineConfigs, AMOUNTX]);

  return (
    <group>
      <group ref={spriteRef}>{Sprites}</group>
      <points ref={particlesRef} />
      {Lines}
    </group>
  );
};